'use client';
import {useState, useEffect, useRef, useCallback} from 'react';
import {
  View,
  Text,
  FlatList,
  Image,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  ActivityIndicator,
  SafeAreaView,
  StatusBar,
  ToastAndroid,
  Platform,
  Alert,
  ScrollView,
  Modal,
} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {TabView, SceneMap, TabBar} from 'react-native-tab-view';
import {useSelector, useDispatch} from 'react-redux';
import {useNavigation, useRoute, RouteProp} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {
  setSelectedBranch,
  setOrderType,
  resetCartData,
  resetUniqueOrderId,
  setBusinessHours,
} from '../store/orderSlice/index';
import {
  useAddToCart,
  formatPrice,
  getFirstOptionPrice,
} from '../utils/cartUtils';
import {useTheme} from '../theme/ThemeProvider';
import CartScreen from './CartScreen';
import {API_ENDPOINTS} from '../config';
import CustomDrawer from '../components/CustomDrawer';
import {Card} from '../components/ui/Card';
import {Button} from '../components/ui/BUtton';
import DropDownPicker from 'react-native-dropdown-picker';
import {checkBusinessHours} from '../utils/businessHoursUtils';

// Define types for navigation and route
type RootStackParamList = {
  Main: {screen: string; params?: any};
  ShopTab: {selectedCategoryId?: string; showCart?: boolean};
  ItemDetail: {item: any};
  Search: undefined;
  Checkout: undefined;
  Splash: undefined;
};

type ShopScreenNavigationProp = StackNavigationProp<RootStackParamList>;
type ShopScreenRouteProp = RouteProp<RootStackParamList, 'ShopTab'>;

// Define types for Redux state
interface OrderState {
  allCategories: any[];
  allItems: any[];
  selectedBranch: any;
  businessId: string;
  orderType: string;
  allBranches: any[];
  hasDelivery: boolean;
  hasPickup: boolean;
  currency: string;
  cart: any;
  logo: string;
  businessHours: any[];
}

interface RootState {
  order: OrderState;
  auth: {
    isLoggedIn: boolean;
  };
}

// Define types for branch items
interface BranchItem {
  label: string;
  value: string | null;
}

// Define type for category
interface Category {
  category_id: string;
  category_name: string;
}
// Import Carousel for swipe functionality
import Carousel from 'react-native-reanimated-carousel';

const {width} = Dimensions.get('window');

// Helper function to strip HTML tags
const stripHtmlTags = (html: string | undefined): string => {
  if (!html) return '';
  return html.replace(/(<([^>]+)>)/gi, '');
};

const ShopScreen = () => {
  const {theme, typography} = useTheme();
  const navigation = useNavigation<StackNavigationProp<RootStackParamList>>();
  const route = useRoute<RouteProp<RootStackParamList, 'ShopTab'>>();
  const dispatch = useDispatch();
  const insets = useSafeAreaInsets();
  const categoryScrollViewRef = useRef(null);
  const swiperRef = useRef<any>(null);

  // Calculate proper height for the carousel container
  const getCarouselHeight = () => {
    const screenHeight = Dimensions.get('window').height;
    const statusBarHeight =
      Platform.OS === 'android' ? StatusBar.currentHeight || 0 : 0;
    const headerHeight = Platform.OS === 'android' ? statusBarHeight + 60 : 60; // Header + status bar
    const categoriesHeight = 60; // Categories container height
    const tabBarHeight = 80; // Bottom tab bar height
    const safeAreaBottom = insets.bottom;

    // For Android, account for translucent status bar and proper safe areas
    if (Platform.OS === 'android') {
      return (
        screenHeight -
        headerHeight -
        categoriesHeight -
        tabBarHeight -
        safeAreaBottom
      );
    } else {
      return (
        screenHeight -
        headerHeight -
        categoriesHeight -
        tabBarHeight -
        safeAreaBottom
      );
    }
  };

  // Get the selected category ID from navigation params if available
  const initialCategoryId = route.params?.selectedCategoryId || 'all';

  // Check if we should show the cart from navigation params
  const showCartFromParams = route.params?.showCart || false;

  // State
  // Add a new state variable to track whether the category change was initiated by a tab click
  const [selectedCategory, setSelectedCategory] = useState(initialCategoryId);
  const [loadingItems, setLoadingItems] = useState({});
  const [isCartVisible, setIsCartVisible] = useState(false);
  const [isDrawerVisible, setIsDrawerVisible] = useState(false);
  const [isModalVisible, setModalVisible] = useState(false);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [toastType, setToastType] = useState('success'); // success or error
  // We'll add the swiper index state after combinedCategories is defined

  // Add refs to track user interactions
  const isTabClickRef = useRef(false);
  const isSwiping = useRef(false);
  const isFromFeaturedCategoryRef = useRef(false);
  const isScrollingTabsRef = useRef(false);

  // Branch selector dropdown state
  const [open, setOpen] = useState(false);
  const [items, setItems] = useState<BranchItem[]>([]);
  const [selectedBranchId, setSelectedBranchId] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState('');

  // Add a state variable to track if multiple branches are available
  const [hasMultipleBranches, setHasMultipleBranches] = useState(false);

  // Redux state
  const {
    allCategories,
    allItems,
    selectedBranch,
    businessId,
    orderType,
    allBranches,
    hasDelivery,
    hasPickup,
    currency,
    cart,
    logo,
    businessHours,
  } = useSelector((state: RootState) => state.order);

  const isLoggedIn = useSelector((state: RootState) => state.auth.isLoggedIn);

  // Create a combined categories array with "all" as the first option
  const combinedCategories = [
    {category_id: 'all', category_name: 'All'},
    ...allCategories,
  ];

  // Add state to track the current swiper index
  const [currentSwiperIndex, setCurrentSwiperIndex] = useState(
    combinedCategories.findIndex(
      cat => cat.category_id === initialCategoryId,
    ) || 0,
  );

  // TabView state for Android
  const [tabIndex, setTabIndex] = useState(
    combinedCategories.findIndex(
      cat => cat.category_id === initialCategoryId,
    ) || 0,
  );

  // Create routes for TabView
  const routes = combinedCategories.map(category => ({
    key: category.category_id,
    title: category.category_name,
  }));

  // Use the useAddToCart hook
  const {addToCart, isLoading: isAddToCartLoading} = useAddToCart();

  // Determine availability based on selectedBranch
  const deliveryAvailable = selectedBranch && selectedBranch.delivery === '1';
  const pickupAvailable = selectedBranch && selectedBranch.pickup === '1';

  // Filter items based on selected category
  const filteredItems =
    selectedCategory === 'all'
      ? allItems
      : allItems.filter(item => item.menu_cat_id === selectedCategory);

  useEffect(() => {
    fetchBranches();
  }, []);

  // Show cart when navigated with showCart parameter
  useEffect(() => {
    if (showCartFromParams) {
      setIsCartVisible(true);
      // Reset the parameter to avoid showing cart again on screen focus
      navigation.setParams({showCart: false});
    }
  }, [showCartFromParams, navigation]);

  // Update selectedCategory and currentSwiperIndex when route.params?.selectedCategoryId changes
  useEffect(() => {
    if (route.params?.selectedCategoryId) {
      const categoryId = route.params.selectedCategoryId;

      // Mark that we're coming from a featured category click
      isFromFeaturedCategoryRef.current = true;

      // Update the selected category
      setSelectedCategory(categoryId);

      // Find the index of the selected category
      const categoryIndex = combinedCategories.findIndex(
        cat => cat.category_id === categoryId,
      );

      // Update the current swiper index if a valid index is found
      if (categoryIndex !== -1) {
        setCurrentSwiperIndex(categoryIndex);

        // Also update TabView index for Android
        if (Platform.OS === 'android') {
          setTabIndex(categoryIndex);
        }

        // Use the carousel's scrollTo method to move to the correct position (iOS only)
        if (Platform.OS === 'ios' && swiperRef.current) {
          setTimeout(() => {
            swiperRef.current.scrollTo({
              index: categoryIndex,
              animated: true,
            });
          }, 100);
        }
      }
    }

    // Cleanup function to reset the flag when component unmounts or effect runs again
    return () => {
      isFromFeaturedCategoryRef.current = false;
    };
  }, [route.params?.selectedCategoryId, combinedCategories]);

  // Scroll to the selected category when the component mounts or when selectedCategory changes
  // but only if it wasn't triggered by a swipe (which already handles scrolling)
  useEffect(() => {
    // Skip scrolling if we're coming from a featured category or if a swipe is in progress
    if (isFromFeaturedCategoryRef.current || isScrollingTabsRef.current) {
      return;
    }

    if (selectedCategory !== 'all' && categoryScrollViewRef.current) {
      // Find the index of the selected category
      const categoryIndex = combinedCategories.findIndex(
        cat => cat.category_id === selectedCategory,
      );

      if (categoryIndex > 0) {
        // Set the scrolling flag to prevent duplicate scrolling
        isScrollingTabsRef.current = true;

        // Add a small delay to ensure the ScrollView is rendered
        setTimeout(() => {
          if (categoryScrollViewRef.current) {
            categoryScrollViewRef.current.scrollTo({
              x: categoryIndex * 100 - 50, // Approximate position
              animated: true,
            });
          }

          // Reset the scrolling flag after animation completes
          setTimeout(() => {
            isScrollingTabsRef.current = false;
          }, 300);
        }, 100);
      }
    }
  }, [selectedCategory, combinedCategories]);

  const fetchBranches = async () => {
    try {
      const response = await fetch(
        `${API_ENDPOINTS.BRANCHES}?source=app&restaurant_id=${businessId}`,
      );
      const data = await response.json();

      // Check if there are multiple branches
      setHasMultipleBranches(data.result.length > 1);

      const branchItems = data.result.map(branch => ({
        label: `${branch.address}, ${branch.area}, ${branch.city}`,
        value: branch.branch_id,
      }));
      setItems([{label: 'Select a branch', value: null}, ...branchItems]);

      // If there's only one branch, select it automatically without showing the modal
      if (data.result.length === 1) {
        const singleBranch = data.result[0];
        dispatch(setSelectedBranch(singleBranch));
        setSelectedBranchId(singleBranch.branch_id);
      }
    } catch (error) {
      console.error('Error fetching branches:', error);
    }
  };

  const handleBranchSelect = async branchId => {
    if (branchId) {
      const selectedBranchObj = items.find(item => item.value === branchId);
      if (selectedBranchObj) {
        dispatch(setSelectedBranch(selectedBranchObj));
        setSelectedBranchId(branchId);
      } else {
        Alert.alert('Error', 'Selected branch not found. Please try again.');
      }
    }
  };

  // Calculate total item count for cart badge
  const getTotalItemCount = () => {
    if (cart && cart.items) {
      return cart.items.reduce((total, item) => total + (item.dqty || 0), 0);
    }
    return 0;
  };

  // Handle category selection
  const handleCategorySelect = categoryId => {
    // Set the ref to indicate this change was initiated by a tab click
    isTabClickRef.current = true;

    // Set the scrolling flag to prevent duplicate scrolling
    isScrollingTabsRef.current = true;

    // Update the selected category state
    setSelectedCategory(categoryId);

    // Find the index of the selected category
    const index = combinedCategories.findIndex(
      cat => cat.category_id === categoryId,
    );

    // Update the current swiper index
    if (index !== -1) {
      setCurrentSwiperIndex(index);
      // Also update TabView index for Android
      if (Platform.OS === 'android') {
        setTabIndex(index);
      }
    }

    // Use the carousel's scrollTo method to move to the correct position (iOS only)
    if (Platform.OS === 'ios' && index !== -1 && swiperRef.current) {
      swiperRef.current.scrollTo({index, animated: true});
    }

    // Scroll the category tabs to show the selected tab
    if (categoryScrollViewRef.current) {
      // Calculate a position that centers the selected tab
      const tabWidth = 100; // Approximate width of a tab
      const screenWidth = width;
      const scrollPosition = Math.max(
        0,
        index * tabWidth - screenWidth / 2 + tabWidth / 2,
      );

      categoryScrollViewRef.current.scrollTo({
        x: scrollPosition,
        animated: true,
      });
    }

    // Reset the flags after animation completes
    setTimeout(() => {
      isTabClickRef.current = false;
      isScrollingTabsRef.current = false;
    }, 300);
  };

  // Add this function to handle manual category navigation
  // Add this after the handleCategorySelect function (around line 200-220)

  // Handle manual category navigation
  const navigateCategory = useCallback(
    (direction: 'next' | 'prev') => {
      // Use the current swiper index
      const currentIndex = currentSwiperIndex;

      if (currentIndex === -1) return;

      let newIndex;
      if (direction === 'next') {
        // Go to next category if not at the end
        newIndex =
          currentIndex < combinedCategories.length - 1
            ? currentIndex + 1
            : currentIndex;
      } else {
        // Go to previous category if not at the beginning
        newIndex = currentIndex > 0 ? currentIndex - 1 : currentIndex;
      }

      // Only update if the index changed
      if (newIndex !== currentIndex) {
        const newCategoryId = combinedCategories[newIndex].category_id;

        // Set the ref to indicate this change was initiated by a programmatic action
        isTabClickRef.current = true;

        // Update the current swiper index
        setCurrentSwiperIndex(newIndex);

        // Update the selected category
        setSelectedCategory(newCategoryId);

        // Use the carousel's snapToItem method to move to the correct position (iOS only)
        if (Platform.OS === 'ios' && swiperRef.current) {
          swiperRef.current.scrollTo({index: newIndex, animated: true});
        }

        // Scroll the category tabs to show the selected tab
        if (categoryScrollViewRef.current) {
          const tabWidth = 100; // Approximate width of a tab
          const screenWidth = width;
          const scrollPosition = Math.max(
            0,
            newIndex * tabWidth - screenWidth / 2 + tabWidth / 2,
          );

          categoryScrollViewRef.current.scrollTo({
            x: scrollPosition,
            animated: true,
          });
        }

        // Reset the ref after a very short delay
        // This allows the swiper to respond to user swipes right after a programmatic navigation
        // without skipping indexes
        setTimeout(() => {
          isTabClickRef.current = false;
        }, 50);
      }
    },
    [combinedCategories, currentSwiperIndex],
  );

  // Show toast message
  const showToastMessage = (message, type = 'success') => {
    setToastMessage(message);
    setToastType(type);
    setShowToast(true);
    setTimeout(() => setShowToast(false), 2000);
  };

  // Render scene for TabView (Android)
  const renderScene = ({route}) => {
    const items =
      route.key === 'all'
        ? allItems
        : allItems.filter(item => item.menu_cat_id === route.key);

    return (
      <View style={styles.tabViewScene}>
        {items.length > 0 ? (
          <FlatList
            data={items}
            renderItem={renderProductItem}
            keyExtractor={item => item.menu_item_id}
            numColumns={2}
            contentContainerStyle={styles.productGrid}
            columnWrapperStyle={styles.productRow}
            showsVerticalScrollIndicator={true}
            scrollEventThrottle={16}
            removeClippedSubviews={true}
            maxToRenderPerBatch={10}
            windowSize={10}
            initialNumToRender={6}
            bounces={false}
            overScrollMode="always"
            scrollEnabled={true}
            nestedScrollEnabled={true}
            style={styles.androidFlatList}
          />
        ) : (
          <View style={styles.emptyStateContainer}>
            <Icon name="inventory" size={64} color="#CCCCCC" />
            <Text style={styles.emptyStateText}>No items available</Text>
          </View>
        )}
      </View>
    );
  };

  // Handle add to cart
  const handleAddToCart = async product => {
    // Check if business is closed
    if (checkBusinessHours(businessHours)) {
      return; // Don't proceed if business is closed
    }

    setLoadingItems(prevState => ({
      ...prevState,
      [product.menu_item_id]: true,
    }));

    // Check if the item has options
    const hasValidOptions = product.options && product.options.length > 0;

    if (hasValidOptions) {
      // If item has options, navigate to detail screen
      navigation.navigate('ItemDetail', {item: product});
      setLoadingItems(prevState => ({
        ...prevState,
        [product.menu_item_id]: false,
      }));
      return;
    }

    // If no options, add directly to cart
    const result = await addToCart(product, 'add', 'new');

    if (!result.success) {
      Alert.alert('Error', result.message);
    } else {
      // Show toast message
      if (Platform.OS === 'android') {
        ToastAndroid.show('Item added successfully', ToastAndroid.SHORT);
      } else {
        showToastMessage('Item added successfully', 'success');
      }
    }

    setLoadingItems(prevState => ({
      ...prevState,
      [product.menu_item_id]: false,
    }));
  };

  const onProceed = () => {
    if (!selectedBranchId) {
      setErrorMessage('Please select a branch to proceed.');
      return;
    }

    dispatch(
      setSelectedBranch(items.find(item => item.value === selectedBranchId)),
    );
    setModalVisible(false);
  };

  // Handle cart closed with empty cart
  const handleCartClosed = isEmpty => {
    setIsCartVisible(false);
    if (isEmpty) {
      showToastMessage('Cart empty', 'error');
    }
  };

  // Render product item - using the same card as HomeScreen
  const renderProductItem = ({item, index}) => {
    const isItemLoading = loadingItems[item.menu_item_id];
    const hasDiscount = item.discount && item.discount > 0;

    // Get the price to display - either the product price or the first option price
    const {
      price: displayPrice,
      showFromText,
      isAvailable,
    } = getFirstOptionPrice(item);

    const discountedPrice = hasDiscount
      ? displayPrice - displayPrice * (item.discount / 100)
      : displayPrice;

    // Determine if the product is unavailable (status === "1")
    const isUnavailable = !isAvailable;

    return (
      <TouchableOpacity
        key={item.menu_item_id || index}
        style={styles.productCard}
        onPress={() => navigation.navigate('ItemDetail', {item})}
        activeOpacity={0.9}
        disabled={isUnavailable}>
        {/* Grey overlay for unavailable products */}
        {isUnavailable && <View style={styles.unavailableProductOverlay} />}

        {/* Product Image */}
        <View style={styles.productImageContainer}>
          {item.image && !item.image.includes('no_image') ? (
            <Image
              source={{uri: item.image}}
              style={styles.productImage}
              resizeMode="cover"
            />
          ) : (
            <View style={styles.noImageContainer}>
              {logo ? (
                <Image
                  source={{uri: logo}}
                  style={{width: 40, height: 40}}
                  resizeMode="contain"
                />
              ) : (
                <Icon name="image" size={30} color="#666" />
              )}
            </View>
          )}
        </View>

        {/* Product Title */}
        <Text
          style={styles.productTitle}
          numberOfLines={1}
          ellipsizeMode="tail">
          {item.name}
        </Text>

        {/* Product Description */}
        <Text
          style={styles.productDescription}
          numberOfLines={2}
          ellipsizeMode="tail">
          {item.desc ? stripHtmlTags(item.desc) : ''}
        </Text>

        {/* Price and Add Button Row */}
        <View style={styles.priceActionRow}>
          <View style={styles.priceContainer}>
            {isUnavailable ? (
              <Text style={[styles.currentPrice, {color: '#FF3B30'}]}>
                Not Available
              </Text>
            ) : hasDiscount ? (
              <View style={styles.priceRow}>
                <Text style={styles.currentPrice}>
                  {showFromText ? 'From ' : ''}
                  {formatPrice(discountedPrice, currency)}
                </Text>
                <Text style={styles.originalPrice}>
                  {formatPrice(item.price, currency)}
                </Text>
              </View>
            ) : (
              <Text style={styles.currentPrice}>
                {showFromText ? 'From ' : ''}
                {formatPrice(discountedPrice, currency)}
              </Text>
            )}
          </View>

          {/* Only show add button for available products */}
          {!isUnavailable && (
            <TouchableOpacity
              style={styles.addButton}
              onPress={e => {
                e.stopPropagation();
                handleAddToCart(item);
              }}
              disabled={isItemLoading}>
              {isItemLoading ? (
                <ActivityIndicator size="small" color="#FFFFFF" />
              ) : (
                <Icon name="add" size={20} color="#FFFFFF" />
              )}
            </TouchableOpacity>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  // We no longer need the renderCategoryPage function as we're directly rendering the selected category

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar
        barStyle="dark-content"
        backgroundColor={Platform.OS === 'android' ? 'transparent' : '#FFFFFF'}
        translucent={Platform.OS === 'android'}
      />

      {/* Header with location and toggle - same as HomeScreen */}
      <View style={styles.headerContainer}>
        {/* Left side with menu and location */}
        <View style={styles.headerLeft}>
          <TouchableOpacity
            onPress={() => setIsDrawerVisible(true)}
            style={styles.menuButton}>
            <Icon name="menu" size={24} color="#000000" />
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              // Only proceed if there are multiple branches
              if (hasMultipleBranches) {
                // Check if cart is empty
                const isCartEmpty = !cart?.items || cart.items.length === 0;

                if (!isCartEmpty) {
                  // If cart is not empty, show confirmation alert
                  Alert.alert(
                    'Change Branch',
                    'Changing the branch will empty your cart. Do you want to continue?',
                    [
                      {
                        text: 'No',
                        style: 'cancel',
                      },
                      {
                        text: 'Yes',
                        onPress: () => {
                          // Empty the cart and reset unique order ID
                          dispatch(resetCartData());
                          dispatch(resetUniqueOrderId());
                          navigation.navigate('Splash');
                        },
                      },
                    ],
                  );
                } else {
                  // If cart is empty, directly navigate to splash screen
                  navigation.navigate('Splash');
                }
              }
            }}
            style={[
              styles.locationSelector,
              !hasMultipleBranches && styles.disabledLocationSelector,
            ]}>
            <Text style={styles.deliveryLabel}>
              {orderType === 'delivery' ? 'Delivering from' : 'Pickup from'}
              {hasMultipleBranches && (
                <Icon name="keyboard-arrow-down" size={16} color="#000000" />
              )}
            </Text>
            <Text style={styles.locationText} numberOfLines={1}>
              {selectedBranch && selectedBranch.address
                ? selectedBranch.address
                : 'Select location'}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Right side with search icon */}
        <TouchableOpacity
          style={styles.searchButton}
          onPress={() => navigation.navigate('Search')}>
          <Icon name="search" size={24} color="#000000" />
        </TouchableOpacity>
      </View>

      {/* Category Filters */}
      <View style={styles.categoriesContainer}>
        <ScrollView
          ref={categoryScrollViewRef}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.categoriesScrollContent}>
          {combinedCategories.map((category, index) => (
            <TouchableOpacity
              key={category.category_id}
              style={[
                styles.categoryButton,
                selectedCategory === category.category_id &&
                  styles.selectedCategoryButton,
              ]}
              onPress={() => handleCategorySelect(category.category_id)}>
              <Text
                style={[
                  styles.categoryButtonText,
                  selectedCategory === category.category_id &&
                    styles.selectedCategoryButtonText,
                ]}>
                {category.category_name}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Product Grid with Carousel for swipe functionality */}
      <View style={styles.productGridContainer}>
        {Platform.OS === 'android' ? (
          // Android: Use TabView for horizontal swipe with proper vertical scroll
          <TabView
            navigationState={{index: tabIndex, routes}}
            renderScene={renderScene}
            onIndexChange={index => {
              setTabIndex(index);
              const newCategoryId = combinedCategories[index].category_id;
              setSelectedCategory(newCategoryId);
              setCurrentSwiperIndex(index);

              // Update the category tabs scroll position
              if (categoryScrollViewRef.current) {
                const tabWidth = 100;
                const screenWidth = width;
                const scrollPosition = Math.max(
                  0,
                  index * tabWidth - screenWidth / 2 + tabWidth / 2,
                );
                categoryScrollViewRef.current.scrollTo({
                  x: scrollPosition,
                  animated: true,
                });
              }
            }}
            initialLayout={{width}}
            renderTabBar={() => null} // Hide the default tab bar since we have our own
            swipeEnabled={true}
            animationEnabled={true}
            style={styles.tabView}
          />
        ) : (
          // iOS: Use Carousel as before
          <Carousel
            ref={swiperRef}
            data={combinedCategories}
            renderItem={({item: category}) => {
              // Get items for the current category
              const items =
                category.category_id === 'all'
                  ? allItems
                  : allItems.filter(
                      item => item.menu_cat_id === category.category_id,
                    );

              return (
                <View style={styles.swiperSlide}>
                  {items.length > 0 ? (
                    <FlatList
                      data={items}
                      renderItem={renderProductItem}
                      keyExtractor={item => item.menu_item_id}
                      numColumns={2}
                      contentContainerStyle={styles.productGrid}
                      columnWrapperStyle={styles.productRow}
                      showsVerticalScrollIndicator={true}
                      scrollEventThrottle={16}
                      maxToRenderPerBatch={10}
                      windowSize={10}
                      initialNumToRender={6}
                      bounces={true}
                      scrollEnabled={true}
                      directionalLockEnabled={false}
                      alwaysBounceVertical={true}
                    />
                  ) : (
                    <View style={styles.emptyStateContainer}>
                      <Icon name="inventory" size={64} color="#CCCCCC" />
                      <Text style={styles.emptyStateText}>
                        No items available
                      </Text>
                    </View>
                  )}
                </View>
              );
            }}
            width={width}
            height={getCarouselHeight()}
            defaultIndex={currentSwiperIndex}
            loop={false}
            enabled={true}
            onSnapToItem={index => {
              // Update the current swiper index
              setCurrentSwiperIndex(index);

              // Process all index changes, but handle differently based on source
              if (index >= 0 && index < combinedCategories.length) {
                const newCategoryId = combinedCategories[index].category_id;

                // If this change was initiated by a tab click, we've already updated the state
                if (isTabClickRef.current) {
                  // Just ensure the flag is reset after a very short delay
                  setTimeout(() => {
                    isTabClickRef.current = false;
                  }, 50);
                  return;
                }

                // If we're coming from a featured category navigation, allow swiping
                // by resetting the flag after the first swipe
                if (isFromFeaturedCategoryRef.current) {
                  isFromFeaturedCategoryRef.current = false;
                }

                // Update the selected category
                setSelectedCategory(newCategoryId);

                // Prevent duplicate scrolling by setting the scrolling flag
                isScrollingTabsRef.current = true;

                // Scroll to the selected category in the horizontal ScrollView
                if (categoryScrollViewRef.current) {
                  // Calculate a position that centers the selected tab
                  const tabWidth = 100; // Approximate width of a tab
                  const screenWidth = width;
                  const scrollPosition = Math.max(
                    0,
                    index * tabWidth - screenWidth / 2 + tabWidth / 2,
                  );

                  categoryScrollViewRef.current.scrollTo({
                    x: scrollPosition,
                    animated: true,
                  });

                  // Reset the scrolling flag after animation completes
                  setTimeout(() => {
                    isScrollingTabsRef.current = false;
                  }, 300);
                }
              }
            }}
            mode="parallax"
            modeConfig={{
              parallaxScrollingScale: 1.0, // No scaling to show full width
              parallaxScrollingOffset: 0, // No offset to show full width
            }}
            scrollAnimationDuration={300}
          />
        )}
      </View>

      {/* Toast Message */}
      {showToast && (
        <View
          style={[
            styles.toast,
            toastType === 'success' ? styles.successToast : styles.errorToast,
          ]}>
          <Text style={styles.toastText}>{toastMessage}</Text>
        </View>
      )}

      {/* Cart Modal */}
      <CartScreen
        visible={isCartVisible}
        onClose={isEmpty => handleCartClosed(isEmpty)}
        onProceedToCheckout={() => navigation.navigate('Checkout')}
      />

      {/* Branch Selection Modal - same as HomeScreen */}
      <Modal
        transparent={true}
        visible={isModalVisible}
        animationType="slide"
        onRequestClose={() => setModalVisible(false)}>
        <View style={styles.modalContainer}>
          <Card style={styles.modalContent} elevation={3}>
            <Text
              style={[
                styles.modalTitle,
                {color: theme.text, ...typography.h3},
              ]}>
              Choose Your Preference
            </Text>

            <View style={styles.toggleContainer}>
              <View style={[styles.iconToggle, {borderColor: theme.border}]}>
                {hasDelivery && (
                  <TouchableOpacity
                    onPress={() => dispatch(setOrderType('delivery'))}
                    style={[
                      styles.iconButton,
                      orderType === 'delivery'
                        ? {backgroundColor: theme.primary}
                        : {backgroundColor: theme.surface},
                    ]}>
                    <Text
                      style={[
                        styles.iconLabel,
                        orderType === 'delivery'
                          ? {color: '#FFFFFF', fontWeight: 'bold'}
                          : {color: theme.textSecondary},
                      ]}>
                      Delivery
                    </Text>
                  </TouchableOpacity>
                )}

                {hasPickup && (
                  <TouchableOpacity
                    onPress={() => dispatch(setOrderType('pickup'))}
                    style={[
                      styles.iconButton,
                      orderType === 'pickup'
                        ? {backgroundColor: theme.primary}
                        : {backgroundColor: theme.surface},
                    ]}>
                    <Text
                      style={[
                        styles.iconLabel,
                        orderType === 'pickup'
                          ? {color: '#FFFFFF', fontWeight: 'bold'}
                          : {color: theme.textSecondary},
                      ]}>
                      Pickup
                    </Text>
                  </TouchableOpacity>
                )}
              </View>
            </View>

            <Text
              style={[
                styles.sectionTitle,
                {color: theme.text, ...typography.h5},
              ]}>
              Select Branch
            </Text>

            <View style={styles.pickerContainer}>
              <DropDownPicker
                open={open}
                value={selectedBranchId}
                items={items}
                setOpen={setOpen}
                setValue={setSelectedBranchId}
                setItems={setItems}
                placeholder="Select a branch"
                style={[
                  styles.dropdown,
                  {
                    backgroundColor: theme.surface,
                    borderColor: theme.border,
                  },
                ]}
                dropDownContainerStyle={[
                  styles.dropdownContainer,
                  {
                    backgroundColor: theme.surface,
                    borderColor: theme.border,
                  },
                ]}
                textStyle={[styles.dropdownText, {color: theme.text}]}
                placeholderStyle={[
                  styles.placeholderText,
                  {color: theme.textLight},
                ]}
                onChangeValue={value => {
                  setSelectedBranchId(value);
                  if (value) {
                    setErrorMessage('');
                  }
                }}
                listItemLabelStyle={[styles.listItemLabel, {color: theme.text}]}
                selectedItemLabelStyle={[
                  styles.selectedItemLabel,
                  {color: theme.primary},
                ]}
                maxHeight={150}
              />
            </View>

            {errorMessage !== '' && (
              <Text style={[styles.errorText, {color: theme.error}]}>
                {errorMessage}
              </Text>
            )}

            <Button
              title="Start My Order"
              onPress={onProceed}
              disabled={!selectedBranchId}
              fullWidth
              size="large"
              style={styles.startButton}
            />
          </Card>
        </View>
      </Modal>

      {/* Custom Drawer - same as HomeScreen */}
      <CustomDrawer
        visible={isDrawerVisible}
        onClose={() => setIsDrawerVisible(false)}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingTop:
      Platform.OS === 'android' ? (StatusBar.currentHeight || 0) + 12 : 12,
    backgroundColor: '#FFFFFF',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  menuButton: {
    padding: 4,
    marginRight: 12,
  },
  locationSelector: {
    flex: 0,
  },
  deliveryLabel: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 2,
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#000000',
  },
  searchButton: {
    padding: 8,
  },
  categoriesContainer: {
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  categoriesScrollContent: {
    paddingHorizontal: 16,
  },
  categoryButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
    marginRight: 10,
    backgroundColor: '#F5F5F5',
  },
  selectedCategoryButton: {
    backgroundColor: '#000000',
  },
  categoryButtonText: {
    fontSize: 14,
    color: '#666666',
    fontWeight: '500',
  },
  selectedCategoryButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  productGridContainer: {
    flex: 1,
    position: 'relative',
    ...(Platform.OS === 'android' && {
      minHeight: 0, // Ensure proper height calculation on Android
    }),
  },
  androidProductContainer: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  androidFlatList: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  tabView: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  tabViewScene: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  swiperSlide: {
    flex: 1,
    ...(Platform.OS === 'android' && {
      minHeight: 0, // Ensure proper height calculation on Android
    }),
  },
  productGrid: {
    padding: 4,
    paddingBottom: Platform.OS === 'android' ? 80 : 60, // Extra padding for Android to account for navigation
    flexGrow: 1,
  },
  productRow: {
    justifyContent: 'space-between',
  },
  // Using the same product card styles as HomeScreen
  productCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    overflow: 'hidden',
    width: (width - 16) / 2, // Reduced margin to use more screen width
    marginBottom: 16,
    elevation: 3,
    shadowColor: '#000000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    margin: 1,
  },
  productImageContainer: {
    width: '100%',
    height: 140,
    backgroundColor: '#F8F8F8',
  },
  productImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  noImageContainer: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F8F8F8',
  },
  productTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginTop: 8,
    marginBottom: 4,
    paddingHorizontal: 8,
  },
  productDescription: {
    fontSize: 12,
    color: '#666666',
    paddingHorizontal: 8,
    marginBottom: 4,
    lineHeight: 16,
    height: 32,
  },
  priceActionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingBottom: 8,
    marginTop: 16,
  },
  priceContainer: {
    flexDirection: 'column',
  },
  priceRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  originalPrice: {
    fontSize: 13,
    color: '#888888',
    textDecorationLine: 'line-through',
    marginLeft: 8,
  },
  currentPrice: {
    fontSize: 15,
    fontWeight: 'bold',
    color: '#000000',
  },
  addButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#000000',
    justifyContent: 'center',
    alignItems: 'center',
  },
  toast: {
    position: 'absolute',
    bottom: 10, // Increased to ensure it appears above the bottom navigation
    left: 20,
    right: 20,
    padding: 12,
    borderRadius: 100,
    alignItems: 'center',
  },
  successToast: {
    backgroundColor: 'rgba(0, 128, 0, 1)',
  },
  errorToast: {
    backgroundColor: 'rgba(255, 0, 0, 1)',
  },
  toastText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
  },
  bottomNavigation: {
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: '#EEEEEE',
    paddingVertical: 8,
    backgroundColor: '#FFFFFF',
  },
  navItem: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
  },
  navItemText: {
    fontSize: 12,
    color: '#AAAAAA',
    marginTop: 4,
  },
  navItemTextActive: {
    fontSize: 12,
    color: '#000000',
    fontWeight: 'bold',
    marginTop: 4,
  },
  cartIconContainer: {
    position: 'relative',
  },
  cartBadge: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: '#FF3B30',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  cartBadgeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  // Modal styles for branch selection
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    padding: 20,
  },
  modalContent: {
    width: '100%',
    maxWidth: 400,
    padding: 24,
    minHeight: 380,
  },
  modalTitle: {
    marginBottom: 24,
    textAlign: 'center',
  },
  iconToggle: {
    flexDirection: 'row',
    borderRadius: 8,
    borderWidth: 1,
    overflow: 'hidden',
  },
  iconButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    alignItems: 'center',
    justifyContent: 'center',
    width: 120,
  },
  iconLabel: {
    fontFamily: 'Poppins-Medium',
    fontSize: 14,
  },
  pickerContainer: {
    marginBottom: 16,
    zIndex: 1000,
  },
  dropdown: {
    borderRadius: 8,
    minHeight: 50,
  },
  dropdownContainer: {
    borderRadius: 8,
  },
  dropdownText: {
    fontFamily: 'Poppins-Regular',
    fontSize: 14,
  },
  placeholderText: {
    fontFamily: 'Poppins-Regular',
    fontSize: 14,
  },
  listItemLabel: {
    fontFamily: 'Poppins-Regular',
    fontSize: 14,
  },
  selectedItemLabel: {
    fontFamily: 'Poppins-Medium',
    fontWeight: '600',
  },
  errorText: {
    fontFamily: 'Poppins-Regular',
    fontSize: 12,
    marginBottom: 16,
  },
  startButton: {
    marginTop: 8,
  },
  toggleContainer: {
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    marginTop: 16,
    marginBottom: 12,
  },
  emptyStateContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: Platform.OS === 'android' ? 140 : 120, // Extra padding for Android
  },
  emptyStateText: {
    fontSize: 18,
    color: '#666666',
    marginTop: 16,
    fontWeight: '500',
  },
  disabledLocationSelector: {
    opacity: 0.8,
  },
  branchTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
  },
  locationIcon: {
    marginLeft: 4,
  },
  unavailableProductOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.6)',
    zIndex: 10,
    borderRadius: 8,
  },
});

export default ShopScreen;
